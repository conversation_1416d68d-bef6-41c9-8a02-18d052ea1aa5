# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
iOS 26 Glassmorphism Medical Theme for LLaMA Factory
"""

from ..extras.packages import is_gradio_available

if is_gradio_available():
    import gradio as gr


def create_medical_theme() -> "gr.Theme":
    """
    Create a custom Gradio theme with iOS 26 glassmorphism medical design.

    Returns:
        gr.Theme: Custom medical theme with glassmorphism effects
    """
    return gr.Theme(
        primary_hue="blue",
        secondary_hue="green",
        neutral_hue="slate",
        spacing_size="md",
        radius_size="lg",
        text_size="md",
        font=[
            "Inter",
            "SF Pro Display",
            "-apple-system",
            "BlinkMacSystemFont",
            "Segoe UI",
            "Roboto",
            "sans-serif"
        ],
        font_mono=[
            "SF Mono",
            "Monaco",
            "Inconsolata",
            "Roboto Mono",
            "monospace"
        ]
    )


def get_medical_theme_css() -> str:
    """
    Get additional CSS for enhanced glassmorphism effects.

    Returns:
        str: Additional CSS styles
    """
    from .components.styled import MEDICAL_COMPONENT_CSS

    return f"""
    /* Enhanced glassmorphism effects */
    .gradio-container {{
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
    }}

    /* Smooth transitions for all interactive elements */
    * {{
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }}

    /* Enhanced hover effects */
    button:hover {{
        transform: translateY(-2px) !important;
    }}

    .panel:hover, .card:hover {{
        transform: translateY(-1px) !important;
    }}

    /* Focus states */
    input:focus, textarea:focus, select:focus {{
        transform: translateY(-1px) !important;
    }}

    /* Loading states */
    .loading {{
        animation: pulse 2s infinite !important;
    }}

    @keyframes pulse {{
        0%, 100% {{ opacity: 1; }}
        50% {{ opacity: 0.7; }}
    }}

    {MEDICAL_COMPONENT_CSS}
    """


# Theme configuration constants
MEDICAL_COLORS = {
    "primary": "#007AFF",
    "secondary": "#34C759", 
    "accent": "#5AC8FA",
    "background": "#F8FAFB",
    "surface": "rgba(255, 255, 255, 0.85)",
    "surface_elevated": "rgba(255, 255, 255, 0.95)",
    "text_primary": "#1D1D1F",
    "text_secondary": "#6E6E73",
    "border": "rgba(0, 122, 255, 0.15)",
    "shadow": "0 8px 32px rgba(0, 122, 255, 0.08)",
    "shadow_elevated": "0 16px 64px rgba(0, 122, 255, 0.12)",
    "error": "#FF3B30",
    "warning": "#FF9500",
    "success": "#34C759"
}

MEDICAL_FONTS = {
    "primary": ["Inter", "SF Pro Display", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif"],
    "mono": ["SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "monospace"]
}

MEDICAL_SPACING = {
    "xs": "4px",
    "sm": "8px", 
    "md": "12px",
    "lg": "16px",
    "xl": "24px",
    "xxl": "32px"
}

MEDICAL_RADIUS = {
    "sm": "8px",
    "md": "12px", 
    "lg": "16px",
    "xl": "24px",
    "full": "9999px"
}

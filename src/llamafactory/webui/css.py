# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

CSS = r"""
/* ========================================
   iOS 26 Glassmorphism Medical Theme
   ======================================== */

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap');

/* Root variables for consistent theming */
:root {
  --medical-primary: #007AFF;
  --medical-secondary: #34C759;
  --medical-accent: #5AC8FA;
  --medical-background: #F8FAFB;
  --medical-surface: rgba(255, 255, 255, 0.85);
  --medical-surface-elevated: rgba(255, 255, 255, 0.95);
  --medical-text-primary: #1D1D1F;
  --medical-text-secondary: #6E6E73;
  --medical-border: rgba(0, 122, 255, 0.15);
  --medical-shadow: 0 8px 32px rgba(0, 122, 255, 0.08);
  --medical-shadow-elevated: 0 16px 64px rgba(0, 122, 255, 0.12);
  --medical-blur: blur(20px);
  --medical-radius: 16px;
  --medical-radius-small: 12px;
  --medical-radius-large: 24px;
  --medical-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global body styling */
body {
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  background: linear-gradient(135deg, #F8FAFB 0%, #E8F4FD 100%) !important;
  color: var(--medical-text-primary) !important;
  line-height: 1.6 !important;
}

/* Main container glassmorphism */
.gradio-container {
  background: var(--medical-surface) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  border: 1px solid var(--medical-border) !important;
  border-radius: var(--medical-radius-large) !important;
  box-shadow: var(--medical-shadow-elevated) !important;
  margin: 20px !important;
  padding: 24px !important;
}

/* Tab styling */
.tab-nav {
  background: var(--medical-surface-elevated) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  border-radius: var(--medical-radius) !important;
  padding: 8px !important;
  margin-bottom: 24px !important;
  box-shadow: var(--medical-shadow) !important;
  border: 1px solid var(--medical-border) !important;
}

.tab-nav button {
  background: transparent !important;
  border: none !important;
  border-radius: var(--medical-radius-small) !important;
  padding: 12px 24px !important;
  font-weight: 500 !important;
  font-size: 15px !important;
  color: var(--medical-text-secondary) !important;
  transition: var(--medical-transition) !important;
  margin: 0 4px !important;
}

.tab-nav button:hover {
  background: rgba(0, 122, 255, 0.08) !important;
  color: var(--medical-primary) !important;
  transform: translateY(-1px) !important;
}

.tab-nav button.selected {
  background: linear-gradient(135deg, var(--medical-primary), var(--medical-accent)) !important;
  color: white !important;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3) !important;
  font-weight: 600 !important;
}

/* Input fields glassmorphism */
input, textarea, select {
  background: var(--medical-surface) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  border: 1px solid var(--medical-border) !important;
  border-radius: var(--medical-radius-small) !important;
  padding: 14px 16px !important;
  font-size: 15px !important;
  font-weight: 400 !important;
  color: var(--medical-text-primary) !important;
  transition: var(--medical-transition) !important;
  box-shadow: var(--medical-shadow) !important;
}

input:focus, textarea:focus, select:focus {
  outline: none !important;
  border-color: var(--medical-primary) !important;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1), var(--medical-shadow) !important;
  transform: translateY(-1px) !important;
}

/* Button styling */
button {
  background: linear-gradient(135deg, var(--medical-primary), var(--medical-accent)) !important;
  border: none !important;
  border-radius: var(--medical-radius-small) !important;
  padding: 14px 28px !important;
  font-size: 15px !important;
  font-weight: 600 !important;
  color: white !important;
  cursor: pointer !important;
  transition: var(--medical-transition) !important;
  box-shadow: var(--medical-shadow) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
}

button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px rgba(0, 122, 255, 0.2) !important;
}

button:active {
  transform: translateY(0) !important;
}

/* Secondary button variant */
button.secondary {
  background: var(--medical-surface-elevated) !important;
  color: var(--medical-primary) !important;
  border: 1px solid var(--medical-border) !important;
}

button.secondary:hover {
  background: rgba(0, 122, 255, 0.08) !important;
}

/* Success button variant */
button.success {
  background: linear-gradient(135deg, var(--medical-secondary), #30D158) !important;
}

/* Card/Panel styling */
.panel, .card {
  background: var(--medical-surface) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  border: 1px solid var(--medical-border) !important;
  border-radius: var(--medical-radius) !important;
  padding: 20px !important;
  margin: 12px 0 !important;
  box-shadow: var(--medical-shadow) !important;
  transition: var(--medical-transition) !important;
}

.panel:hover, .card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--medical-shadow-elevated) !important;
}

/* Dropdown styling */
.dropdown {
  background: var(--medical-surface) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  border: 1px solid var(--medical-border) !important;
  border-radius: var(--medical-radius-small) !important;
  box-shadow: var(--medical-shadow) !important;
}

/* Progress bar styling */
.progress {
  background: rgba(0, 122, 255, 0.1) !important;
  border-radius: 50px !important;
  overflow: hidden !important;
  height: 8px !important;
}

.progress-bar {
  background: linear-gradient(90deg, var(--medical-primary), var(--medical-accent)) !important;
  border-radius: 50px !important;
  transition: var(--medical-transition) !important;
}

/* Existing styles with glassmorphism enhancements */
.duplicate-button {
  margin: auto !important;
  background: linear-gradient(135deg, var(--medical-text-primary), #2C2C2E) !important;
  color: white !important;
  border-radius: 100vh !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  box-shadow: var(--medical-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.thinking-summary {
  padding: 12px !important;
  background: var(--medical-surface) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  border-radius: var(--medical-radius-small) !important;
  border: 1px solid var(--medical-border) !important;
  box-shadow: var(--medical-shadow) !important;
}

.thinking-summary span {
  border-radius: var(--medical-radius-small) !important;
  padding: 8px 12px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(0, 122, 255, 0.08) !important;
  color: var(--medical-primary) !important;
  border: 1px solid var(--medical-border) !important;
  transition: var(--medical-transition) !important;
}

.thinking-summary span:hover {
  background: rgba(0, 122, 255, 0.15) !important;
  transform: translateY(-1px) !important;
}

.dark .thinking-summary span {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.thinking-container {
  border-left: 3px solid var(--medical-primary) !important;
  padding-left: 16px !important;
  margin: 8px 0 !important;
  background: rgba(0, 122, 255, 0.02) !important;
  border-radius: 0 var(--medical-radius-small) var(--medical-radius-small) 0 !important;
}

.thinking-container p {
  color: var(--medical-text-secondary) !important;
  font-weight: 400 !important;
}

.modal-box {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  max-width: 1000px !important;
  max-height: 750px !important;
  overflow-y: auto !important;
  background: var(--medical-surface-elevated) !important;
  backdrop-filter: var(--medical-blur) !important;
  -webkit-backdrop-filter: var(--medical-blur) !important;
  flex-wrap: nowrap !important;
  border: 1px solid var(--medical-border) !important;
  border-radius: var(--medical-radius-large) !important;
  box-shadow: var(--medical-shadow-elevated) !important;
  z-index: 1000 !important;
  padding: 24px !important;
}

.dark .modal-box {
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(28, 28, 30, 0.95) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .gradio-container {
    margin: 10px !important;
    padding: 16px !important;
    border-radius: var(--medical-radius) !important;
  }

  .tab-nav button {
    padding: 10px 16px !important;
    font-size: 14px !important;
  }

  input, textarea, select, button {
    padding: 12px 14px !important;
    font-size: 14px !important;
  }
}

/* Animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Loading animations */
.loading {
  animation: pulse 2s infinite !important;
}

/* Fade in animation for new elements */
.fade-in {
  animation: fadeInUp 0.5s ease-out !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --medical-primary: #0A84FF;
    --medical-secondary: #30D158;
    --medical-accent: #64D2FF;
    --medical-background: #000000;
    --medical-surface: rgba(28, 28, 30, 0.85);
    --medical-surface-elevated: rgba(44, 44, 46, 0.95);
    --medical-text-primary: #FFFFFF;
    --medical-text-secondary: #98989D;
    --medical-border: rgba(10, 132, 255, 0.15);
    --medical-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --medical-shadow-elevated: 0 16px 64px rgba(0, 0, 0, 0.4);
  }

  body {
    background: linear-gradient(135deg, #000000 0%, #1C1C1E 100%) !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --medical-border: rgba(0, 122, 255, 0.4);
    --medical-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  button, input, textarea, select {
    border-width: 2px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .gradio-container {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }

  button {
    background: white !important;
    color: black !important;
    border: 1px solid #ccc !important;
  }
}
"""

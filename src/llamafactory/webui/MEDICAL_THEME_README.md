# 🏥 LLaMA Factory Medical Theme

## 概述

这是一个为LLaMA Factory Web UI设计的iOS 26风格玻璃拟态医疗主题。该主题采用现代医疗应用的设计语言，提供优雅、专业且易用的界面体验。

## ✨ 特性

### 🎨 设计风格
- **玻璃拟态效果**: 半透明背景、模糊效果、柔和阴影
- **医疗配色方案**: 以白色、浅蓝色、淡绿色为主色调
- **现代字体**: Inter + SF Pro Display 字体系列
- **圆角设计**: 统一的圆角风格，符合现代UI趋势

### 🔧 功能特性
- **完全兼容**: 保持所有原有功能不变
- **响应式设计**: 适配不同屏幕尺寸
- **无障碍支持**: 支持高对比度和减少动画模式
- **暗色模式**: 自动适配系统暗色模式
- **性能优化**: 不影响界面加载性能

## 📁 文件结构

```
src/llamafactory/webui/
├── css.py                    # 主要CSS样式文件 (已更新)
├── theme.py                  # 自定义Gradio主题配置 (新增)
├── interface.py              # 界面配置 (已更新)
├── components/
│   ├── styled.py            # 样式化组件库 (新增)
│   └── medical_demo.py      # 主题演示组件 (新增)
└── MEDICAL_THEME_README.md  # 主题说明文档 (本文件)
```

## 🚀 使用方法

### 基本使用

主题已自动应用到所有Web UI界面，无需额外配置。

### 使用样式化组件

```python
from llamafactory.webui.components.styled import (
    create_medical_button,
    create_medical_textbox,
    create_medical_card,
    create_medical_info_box
)

# 创建医疗风格按钮
start_btn = create_medical_button("开始训练", variant="primary")

# 创建医疗风格输入框
model_input = create_medical_textbox(
    label="模型名称",
    placeholder="请输入模型名称..."
)

# 创建信息卡片
info_card = create_medical_card(
    title="训练状态",
    content="模型训练进行中..."
)

# 创建信息提示框
success_box = create_medical_info_box(
    "训练完成！",
    type="success"
)
```

### 自定义样式

如需自定义样式，可以修改 `theme.py` 中的颜色配置：

```python
MEDICAL_COLORS = {
    "primary": "#007AFF",      # 主色调
    "secondary": "#34C759",    # 次要色调
    "accent": "#5AC8FA",       # 强调色
    "background": "#F8FAFB",   # 背景色
    # ... 更多配置
}
```

## 🎨 设计规范

### 颜色系统

| 用途 | 颜色值 | 说明 |
|------|--------|------|
| 主色调 | `#007AFF` | 用于主要按钮、链接等 |
| 次要色调 | `#34C759` | 用于成功状态、确认操作 |
| 强调色 | `#5AC8FA` | 用于渐变、高亮效果 |
| 背景色 | `#F8FAFB` | 主要背景色 |
| 表面色 | `rgba(255, 255, 255, 0.85)` | 卡片、面板背景 |
| 文字主色 | `#1D1D1F` | 主要文字颜色 |
| 文字次色 | `#6E6E73` | 次要文字颜色 |

### 字体规范

- **主字体**: Inter, SF Pro Display
- **等宽字体**: SF Mono, Monaco
- **字重**: 300 (Light), 400 (Regular), 500 (Medium), 600 (Semibold), 700 (Bold)

### 间距系统

- **xs**: 4px
- **sm**: 8px
- **md**: 12px (默认)
- **lg**: 16px
- **xl**: 24px
- **xxl**: 32px

### 圆角规范

- **小圆角**: 8px
- **中圆角**: 12px
- **大圆角**: 16px
- **超大圆角**: 24px
- **完全圆角**: 9999px

## 🔧 技术实现

### CSS变量系统

主题使用CSS自定义属性实现一致的样式管理：

```css
:root {
  --medical-primary: #007AFF;
  --medical-surface: rgba(255, 255, 255, 0.85);
  --medical-blur: blur(20px);
  --medical-radius: 16px;
  --medical-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 玻璃拟态效果

```css
.glassmorphism {
  background: var(--medical-surface);
  backdrop-filter: var(--medical-blur);
  -webkit-backdrop-filter: var(--medical-blur);
  border: 1px solid var(--medical-border);
  box-shadow: var(--medical-shadow);
}
```

### 动画系统

- **过渡动画**: 使用 `cubic-bezier(0.4, 0, 0.2, 1)` 缓动函数
- **悬停效果**: 轻微的Y轴位移和阴影变化
- **加载动画**: 脉冲效果和渐入动画
- **响应式动画**: 支持 `prefers-reduced-motion` 媒体查询

## 🌙 暗色模式

主题自动支持系统暗色模式，通过 `prefers-color-scheme: dark` 媒体查询实现：

```css
@media (prefers-color-scheme: dark) {
  :root {
    --medical-primary: #0A84FF;
    --medical-background: #000000;
    --medical-surface: rgba(28, 28, 30, 0.85);
    --medical-text-primary: #FFFFFF;
  }
}
```

## ♿ 无障碍支持

- **高对比度模式**: 支持 `prefers-contrast: high`
- **减少动画**: 支持 `prefers-reduced-motion: reduce`
- **键盘导航**: 优化的焦点状态样式
- **屏幕阅读器**: 语义化的HTML结构

## 📱 响应式设计

主题针对不同屏幕尺寸进行了优化：

- **桌面端**: 完整的玻璃拟态效果
- **平板端**: 适中的间距和字体大小
- **移动端**: 简化的效果，优化触摸交互

## 🔄 更新日志

### v1.0.0 (2025-01-XX)
- ✨ 初始版本发布
- 🎨 实现iOS 26风格玻璃拟态设计
- 🏥 采用医疗应用配色方案
- 📱 支持响应式设计
- 🌙 支持暗色模式
- ♿ 添加无障碍支持

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个主题！

## 📄 许可证

本主题遵循与LLaMA Factory相同的Apache 2.0许可证。

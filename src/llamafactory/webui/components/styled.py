# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Styled components with medical glassmorphism theme
"""

from typing import Any, Optional
from ...extras.packages import is_gradio_available

if is_gradio_available():
    import gradio as gr


def create_medical_button(
    value: str,
    variant: str = "primary",
    size: str = "md",
    **kwargs: Any
) -> "gr.Button":
    """
    Create a styled button with medical theme.
    
    Args:
        value: Button text
        variant: Button style variant (primary, secondary, success, danger)
        size: Button size (sm, md, lg)
        **kwargs: Additional Gradio button parameters
        
    Returns:
        gr.Button: Styled button component
    """
    css_classes = ["medical-button", f"medical-button-{variant}", f"medical-button-{size}"]
    
    return gr.Button(
        value=value,
        elem_classes=css_classes,
        **kwargs
    )


def create_medical_textbox(
    label: Optional[str] = None,
    placeholder: Optional[str] = None,
    **kwargs: Any
) -> "gr.Textbox":
    """
    Create a styled textbox with medical theme.
    
    Args:
        label: Input label
        placeholder: Placeholder text
        **kwargs: Additional Gradio textbox parameters
        
    Returns:
        gr.Textbox: Styled textbox component
    """
    return gr.Textbox(
        label=label,
        placeholder=placeholder,
        elem_classes=["medical-input"],
        **kwargs
    )


def create_medical_dropdown(
    label: Optional[str] = None,
    choices: Optional[list] = None,
    **kwargs: Any
) -> "gr.Dropdown":
    """
    Create a styled dropdown with medical theme.
    
    Args:
        label: Dropdown label
        choices: List of choices
        **kwargs: Additional Gradio dropdown parameters
        
    Returns:
        gr.Dropdown: Styled dropdown component
    """
    return gr.Dropdown(
        label=label,
        choices=choices or [],
        elem_classes=["medical-dropdown"],
        **kwargs
    )


def create_medical_card(title: str, content: Any) -> "gr.HTML":
    """
    Create a styled card component.
    
    Args:
        title: Card title
        content: Card content
        
    Returns:
        gr.HTML: Styled card component
    """
    card_html = f"""
    <div class="medical-card fade-in">
        <div class="medical-card-header">
            <h3 class="medical-card-title">{title}</h3>
        </div>
        <div class="medical-card-content">
            {content}
        </div>
    </div>
    """
    
    return gr.HTML(card_html)


def create_medical_info_box(
    message: str,
    type: str = "info"
) -> "gr.HTML":
    """
    Create a styled information box.
    
    Args:
        message: Information message
        type: Box type (info, success, warning, error)
        
    Returns:
        gr.HTML: Styled info box component
    """
    icons = {
        "info": "ℹ️",
        "success": "✅", 
        "warning": "⚠️",
        "error": "❌"
    }
    
    icon = icons.get(type, "ℹ️")
    
    info_html = f"""
    <div class="medical-info-box medical-info-{type} fade-in">
        <div class="medical-info-icon">{icon}</div>
        <div class="medical-info-message">{message}</div>
    </div>
    """
    
    return gr.HTML(info_html)


def create_medical_progress_bar(
    progress: float = 0.0,
    label: str = "Progress"
) -> "gr.HTML":
    """
    Create a styled progress bar.
    
    Args:
        progress: Progress value (0.0 to 1.0)
        label: Progress label
        
    Returns:
        gr.HTML: Styled progress bar component
    """
    percentage = int(progress * 100)
    
    progress_html = f"""
    <div class="medical-progress-container fade-in">
        <div class="medical-progress-label">{label}</div>
        <div class="medical-progress-bar">
            <div class="medical-progress-fill" style="width: {percentage}%"></div>
        </div>
        <div class="medical-progress-text">{percentage}%</div>
    </div>
    """
    
    return gr.HTML(progress_html)


def create_medical_header(
    title: str,
    subtitle: Optional[str] = None
) -> "gr.HTML":
    """
    Create a styled header component.
    
    Args:
        title: Main title
        subtitle: Optional subtitle
        
    Returns:
        gr.HTML: Styled header component
    """
    subtitle_html = f'<p class="medical-header-subtitle">{subtitle}</p>' if subtitle else ""
    
    header_html = f"""
    <div class="medical-header fade-in">
        <h1 class="medical-header-title">{title}</h1>
        {subtitle_html}
    </div>
    """
    
    return gr.HTML(header_html)


def create_medical_footer() -> "gr.HTML":
    """
    Create a styled footer component.
    
    Returns:
        gr.HTML: Styled footer component
    """
    footer_html = """
    <div class="medical-footer fade-in">
        <div class="medical-footer-content">
            <p class="medical-footer-text">
                🏥 Magic Lab Medical Model Fine-tuning Workshop
            </p>
            <p class="medical-footer-subtext">
                Powered by LLaMA Factory with iOS 26 Glassmorphism Design
            </p>
        </div>
    </div>
    """
    
    return gr.HTML(footer_html)


def create_medical_stats_card(
    title: str,
    value: str,
    change: Optional[str] = None,
    trend: str = "neutral"
) -> "gr.HTML":
    """
    Create a styled statistics card.
    
    Args:
        title: Stat title
        value: Stat value
        change: Change indicator
        trend: Trend direction (up, down, neutral)
        
    Returns:
        gr.HTML: Styled stats card component
    """
    trend_icons = {
        "up": "📈",
        "down": "📉", 
        "neutral": "➡️"
    }
    
    trend_icon = trend_icons.get(trend, "➡️")
    change_html = f'<div class="medical-stats-change medical-stats-{trend}">{trend_icon} {change}</div>' if change else ""
    
    stats_html = f"""
    <div class="medical-stats-card fade-in">
        <div class="medical-stats-title">{title}</div>
        <div class="medical-stats-value">{value}</div>
        {change_html}
    </div>
    """
    
    return gr.HTML(stats_html)


# CSS classes for styled components
MEDICAL_COMPONENT_CSS = """
/* Medical Card Styles */
.medical-card {
    background: var(--medical-surface) !important;
    backdrop-filter: var(--medical-blur) !important;
    -webkit-backdrop-filter: var(--medical-blur) !important;
    border: 1px solid var(--medical-border) !important;
    border-radius: var(--medical-radius) !important;
    padding: 20px !important;
    margin: 12px 0 !important;
    box-shadow: var(--medical-shadow) !important;
    transition: var(--medical-transition) !important;
}

.medical-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--medical-shadow-elevated) !important;
}

.medical-card-header {
    margin-bottom: 16px !important;
}

.medical-card-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: var(--medical-text-primary) !important;
    margin: 0 !important;
}

/* Medical Info Box Styles */
.medical-info-box {
    display: flex !important;
    align-items: center !important;
    padding: 16px !important;
    border-radius: var(--medical-radius-small) !important;
    margin: 12px 0 !important;
    backdrop-filter: var(--medical-blur) !important;
    -webkit-backdrop-filter: var(--medical-blur) !important;
}

.medical-info-icon {
    margin-right: 12px !important;
    font-size: 20px !important;
}

.medical-info-message {
    font-weight: 500 !important;
    flex: 1 !important;
}

.medical-info-info {
    background: rgba(0, 122, 255, 0.1) !important;
    border: 1px solid rgba(0, 122, 255, 0.2) !important;
    color: var(--medical-primary) !important;
}

.medical-info-success {
    background: rgba(52, 199, 89, 0.1) !important;
    border: 1px solid rgba(52, 199, 89, 0.2) !important;
    color: var(--medical-secondary) !important;
}

.medical-info-warning {
    background: rgba(255, 149, 0, 0.1) !important;
    border: 1px solid rgba(255, 149, 0, 0.2) !important;
    color: #FF9500 !important;
}

.medical-info-error {
    background: rgba(255, 59, 48, 0.1) !important;
    border: 1px solid rgba(255, 59, 48, 0.2) !important;
    color: #FF3B30 !important;
}

/* Medical Progress Bar Styles */
.medical-progress-container {
    margin: 16px 0 !important;
}

.medical-progress-label {
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    color: var(--medical-text-primary) !important;
}

.medical-progress-bar {
    background: rgba(0, 122, 255, 0.1) !important;
    border-radius: 50px !important;
    height: 8px !important;
    overflow: hidden !important;
    position: relative !important;
}

.medical-progress-fill {
    background: linear-gradient(90deg, var(--medical-primary), var(--medical-accent)) !important;
    height: 100% !important;
    border-radius: 50px !important;
    transition: width 0.5s ease !important;
}

.medical-progress-text {
    text-align: right !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-top: 4px !important;
    color: var(--medical-text-secondary) !important;
}

/* Medical Header Styles */
.medical-header {
    text-align: center !important;
    margin: 24px 0 !important;
    padding: 24px !important;
    background: var(--medical-surface) !important;
    backdrop-filter: var(--medical-blur) !important;
    -webkit-backdrop-filter: var(--medical-blur) !important;
    border-radius: var(--medical-radius-large) !important;
    border: 1px solid var(--medical-border) !important;
    box-shadow: var(--medical-shadow) !important;
}

.medical-header-title {
    font-size: 32px !important;
    font-weight: 700 !important;
    color: var(--medical-text-primary) !important;
    margin: 0 0 8px 0 !important;
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-accent)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.medical-header-subtitle {
    font-size: 16px !important;
    color: var(--medical-text-secondary) !important;
    margin: 0 !important;
}

/* Medical Footer Styles */
.medical-footer {
    text-align: center !important;
    padding: 24px !important;
    margin-top: 32px !important;
    background: var(--medical-surface) !important;
    backdrop-filter: var(--medical-blur) !important;
    -webkit-backdrop-filter: var(--medical-blur) !important;
    border-radius: var(--medical-radius) !important;
    border: 1px solid var(--medical-border) !important;
    box-shadow: var(--medical-shadow) !important;
}

.medical-footer-text {
    font-weight: 600 !important;
    color: var(--medical-text-primary) !important;
    margin: 0 0 4px 0 !important;
}

.medical-footer-subtext {
    font-size: 14px !important;
    color: var(--medical-text-secondary) !important;
    margin: 0 !important;
}

/* Medical Stats Card Styles */
.medical-stats-card {
    background: var(--medical-surface) !important;
    backdrop-filter: var(--medical-blur) !important;
    -webkit-backdrop-filter: var(--medical-blur) !important;
    border: 1px solid var(--medical-border) !important;
    border-radius: var(--medical-radius) !important;
    padding: 20px !important;
    text-align: center !important;
    transition: var(--medical-transition) !important;
    box-shadow: var(--medical-shadow) !important;
}

.medical-stats-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--medical-shadow-elevated) !important;
}

.medical-stats-title {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: var(--medical-text-secondary) !important;
    margin-bottom: 8px !important;
}

.medical-stats-value {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: var(--medical-text-primary) !important;
    margin-bottom: 4px !important;
}

.medical-stats-change {
    font-size: 12px !important;
    font-weight: 500 !important;
}

.medical-stats-up {
    color: var(--medical-secondary) !important;
}

.medical-stats-down {
    color: #FF3B30 !important;
}

.medical-stats-neutral {
    color: var(--medical-text-secondary) !important;
}
"""

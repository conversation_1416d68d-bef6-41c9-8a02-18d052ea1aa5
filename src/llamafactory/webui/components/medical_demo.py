# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Medical theme demo components
"""

from typing import Dict, Any
from ...extras.packages import is_gradio_available

if is_gradio_available():
    import gradio as gr

from .styled import (
    create_medical_button,
    create_medical_textbox,
    create_medical_dropdown,
    create_medical_card,
    create_medical_info_box,
    create_medical_progress_bar,
    create_medical_header,
    create_medical_footer,
    create_medical_stats_card
)


def create_medical_demo_tab() -> Dict[str, Any]:
    """
    Create a demo tab showcasing the medical theme components.
    
    Returns:
        Dict[str, Any]: Demo tab components
    """
    with gr.Tab("🎨 Theme Demo") as demo_tab:
        # Header
        create_medical_header(
            title="🏥 Medical Theme Showcase",
            subtitle="iOS 26 Glassmorphism Design Components"
        )
        
        # Info boxes
        with gr.Row():
            with gr.Column():
                create_medical_info_box(
                    "This is an information message with glassmorphism effects.",
                    type="info"
                )
                create_medical_info_box(
                    "Training completed successfully with improved accuracy!",
                    type="success"
                )
            with gr.Column():
                create_medical_info_box(
                    "Please check your model configuration before proceeding.",
                    type="warning"
                )
                create_medical_info_box(
                    "Error: Unable to load the specified model file.",
                    type="error"
                )
        
        # Statistics cards
        with gr.Row():
            with gr.Column():
                create_medical_stats_card(
                    title="Training Accuracy",
                    value="94.2%",
                    change="+2.1%",
                    trend="up"
                )
            with gr.Column():
                create_medical_stats_card(
                    title="Model Size",
                    value="7.2GB",
                    change="Stable",
                    trend="neutral"
                )
            with gr.Column():
                create_medical_stats_card(
                    title="Training Time",
                    value="2.5h",
                    change="-15min",
                    trend="down"
                )
            with gr.Column():
                create_medical_stats_card(
                    title="GPU Usage",
                    value="78%",
                    change="+5%",
                    trend="up"
                )
        
        # Progress bars
        with gr.Row():
            with gr.Column():
                create_medical_progress_bar(
                    progress=0.75,
                    label="Training Progress"
                )
                create_medical_progress_bar(
                    progress=0.45,
                    label="Data Processing"
                )
            with gr.Column():
                create_medical_progress_bar(
                    progress=0.90,
                    label="Model Validation"
                )
                create_medical_progress_bar(
                    progress=0.30,
                    label="Export Progress"
                )
        
        # Interactive components
        with gr.Row():
            with gr.Column():
                create_medical_card(
                    title="Model Configuration",
                    content="""
                    <div style="space-y: 12px;">
                        <p><strong>Model Type:</strong> LLaMA-2-7B</p>
                        <p><strong>Fine-tuning Method:</strong> LoRA</p>
                        <p><strong>Learning Rate:</strong> 2e-4</p>
                        <p><strong>Batch Size:</strong> 16</p>
                    </div>
                    """
                )
            with gr.Column():
                create_medical_card(
                    title="Training Metrics",
                    content="""
                    <div style="space-y: 12px;">
                        <p><strong>Loss:</strong> 0.234</p>
                        <p><strong>Perplexity:</strong> 1.26</p>
                        <p><strong>BLEU Score:</strong> 0.89</p>
                        <p><strong>Epochs:</strong> 3/5</p>
                    </div>
                    """
                )
        
        # Styled form components
        with gr.Row():
            with gr.Column():
                gr.Markdown("### 🔧 Styled Form Components")
                
                model_name = create_medical_textbox(
                    label="Model Name",
                    placeholder="Enter your model name..."
                )
                
                model_type = create_medical_dropdown(
                    label="Model Type",
                    choices=["LLaMA-2-7B", "LLaMA-2-13B", "LLaMA-2-70B", "ChatGLM-6B"]
                )
                
                training_method = create_medical_dropdown(
                    label="Training Method",
                    choices=["LoRA", "QLoRA", "Full Fine-tuning", "P-Tuning"]
                )
                
                with gr.Row():
                    start_btn = create_medical_button(
                        "🚀 Start Training",
                        variant="primary"
                    )
                    stop_btn = create_medical_button(
                        "⏹️ Stop Training",
                        variant="secondary"
                    )
                    save_btn = create_medical_button(
                        "💾 Save Model",
                        variant="success"
                    )
            
            with gr.Column():
                gr.Markdown("### 📊 Real-time Monitoring")
                
                # Placeholder for real-time charts
                gr.HTML("""
                <div class="medical-card fade-in">
                    <div class="medical-card-header">
                        <h3 class="medical-card-title">📈 Training Loss Curve</h3>
                    </div>
                    <div class="medical-card-content" style="height: 200px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(90, 200, 250, 0.05)); border-radius: 12px;">
                        <p style="color: var(--medical-text-secondary); font-style: italic;">
                            📊 Interactive training charts will appear here
                        </p>
                    </div>
                </div>
                """)
                
                gr.HTML("""
                <div class="medical-card fade-in">
                    <div class="medical-card-header">
                        <h3 class="medical-card-title">🎯 Accuracy Metrics</h3>
                    </div>
                    <div class="medical-card-content" style="height: 150px; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(52, 199, 89, 0.05), rgba(48, 209, 88, 0.05)); border-radius: 12px;">
                        <p style="color: var(--medical-text-secondary); font-style: italic;">
                            📈 Real-time accuracy monitoring
                        </p>
                    </div>
                </div>
                """)
        
        # Footer
        create_medical_footer()
    
    return {
        "demo_tab": demo_tab,
        "model_name": model_name,
        "model_type": model_type,
        "training_method": training_method,
        "start_btn": start_btn,
        "stop_btn": stop_btn,
        "save_btn": save_btn
    }


def setup_demo_interactions(components: Dict[str, Any]) -> None:
    """
    Setup interactions for demo components.
    
    Args:
        components: Dictionary of demo components
    """
    def on_start_training(model_name, model_type, training_method):
        return f"🚀 Starting {training_method} training for {model_name} ({model_type})..."
    
    def on_stop_training():
        return "⏹️ Training stopped successfully."
    
    def on_save_model(model_name):
        return f"💾 Model '{model_name}' saved successfully!"
    
    # Setup button interactions
    components["start_btn"].click(
        fn=on_start_training,
        inputs=[
            components["model_name"],
            components["model_type"],
            components["training_method"]
        ],
        outputs=[]
    )
    
    components["stop_btn"].click(
        fn=on_stop_training,
        inputs=[],
        outputs=[]
    )
    
    components["save_btn"].click(
        fn=on_save_model,
        inputs=[components["model_name"]],
        outputs=[]
    )

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LLaMA Factory Medical Theme Installation Script
安装医疗主题的脚本
"""

import os
import sys
import shutil
from pathlib import Path


def print_banner():
    """打印安装横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🏥 LLaMA Factory Medical Theme Installer              ║
    ║                                                              ║
    ║        iOS 26 Glassmorphism Medical Design                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_llamafactory_installation():
    """检查LLaMA Factory是否已安装"""
    try:
        import llamafactory
        print("✅ LLaMA Factory 已安装")
        return True
    except ImportError:
        print("❌ 未找到LLaMA Factory，请先安装LLaMA Factory")
        return False


def check_gradio_version():
    """检查Gradio版本"""
    try:
        import gradio as gr
        version = gr.__version__
        print(f"✅ Gradio 版本: {version}")
        
        # 检查版本兼容性
        major, minor = map(int, version.split('.')[:2])
        if major >= 4:
            print("✅ Gradio 版本兼容")
            return True
        else:
            print("⚠️  建议升级Gradio到4.0+以获得最佳体验")
            return True
    except ImportError:
        print("❌ 未找到Gradio")
        return False


def backup_original_files():
    """备份原始文件"""
    print("\n📦 备份原始文件...")
    
    backup_dir = Path("backup_original_theme")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "src/llamafactory/webui/css.py",
        "src/llamafactory/webui/interface.py"
    ]
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"✅ 已备份: {file_path} -> {backup_path}")
        else:
            print(f"⚠️  文件不存在: {file_path}")


def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    required_files = [
        "src/llamafactory/webui/css.py",
        "src/llamafactory/webui/theme.py",
        "src/llamafactory/webui/interface.py",
        "src/llamafactory/webui/components/styled.py",
        "src/llamafactory/webui/components/medical_demo.py",
        "src/llamafactory/webui/MEDICAL_THEME_README.md"
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            all_files_exist = False
    
    return all_files_exist


def test_theme_import():
    """测试主题导入"""
    print("\n🧪 测试主题导入...")
    
    try:
        # 添加src目录到Python路径
        src_path = Path("src").absolute()
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from llamafactory.webui.theme import create_medical_theme, get_medical_theme_css
        from llamafactory.webui.components.styled import create_medical_button
        
        print("✅ 主题模块导入成功")
        
        # 测试主题创建
        theme = create_medical_theme()
        print("✅ 医疗主题创建成功")
        
        # 测试CSS生成
        css = get_medical_theme_css()
        print("✅ CSS样式生成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主题导入失败: {e}")
        return False


def print_usage_instructions():
    """打印使用说明"""
    instructions = """
    
    🎉 医疗主题安装完成！
    
    📋 使用说明:
    
    1. 启动LLaMA Factory Web UI:
       python src/webui.py
       或
       llamafactory-cli webui
    
    2. 打开浏览器访问: http://127.0.0.1:7860
    
    3. 您将看到全新的iOS 26风格医疗主题界面！
    
    🎨 主要特性:
    ✨ 玻璃拟态效果
    🏥 医疗配色方案  
    📱 响应式设计
    🌙 暗色模式支持
    ♿ 无障碍支持
    
    📚 更多信息请查看:
    src/llamafactory/webui/MEDICAL_THEME_README.md
    
    🔧 如需恢复原始主题:
    从 backup_original_theme/ 目录恢复备份文件
    
    """
    print(instructions)


def main():
    """主安装函数"""
    print_banner()
    
    # 检查依赖
    if not check_llamafactory_installation():
        sys.exit(1)
    
    if not check_gradio_version():
        sys.exit(1)
    
    # 备份原始文件
    backup_original_files()
    
    # 验证安装
    if not verify_installation():
        print("\n❌ 安装验证失败，请检查文件是否正确")
        sys.exit(1)
    
    # 测试主题导入
    if not test_theme_import():
        print("\n❌ 主题测试失败，请检查代码是否正确")
        sys.exit(1)
    
    # 打印使用说明
    print_usage_instructions()


if __name__ == "__main__":
    main()

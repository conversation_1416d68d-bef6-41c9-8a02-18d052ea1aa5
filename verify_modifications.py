#!/usr/bin/env python3
"""
验证UI修改的简单脚本
"""

import sys
import os
sys.path.append('src')

def main():
    print("🔍 验证LLaMA-Factory UI修改...")
    
    # 1. 检查locales文件
    try:
        from llamafactory.webui.locales import LOCALES
        print("✅ locales模块加载成功")
        
        # 检查标题
        zh_title = LOCALES["title"]["zh"]["value"]
        if "魔法实验室·医疗模型微调工坊" in zh_title:
            print("✅ 中文标题修改成功")
        else:
            print("❌ 中文标题修改失败")
            
        # 检查副标题（GitHub链接移除）
        zh_subtitle = LOCALES["subtitle"]["zh"]["value"]
        if "江阴市人民医院·睡眠魔法师团队" in zh_subtitle and "github.com" not in zh_subtitle.lower():
            print("✅ 中文副标题修改成功，GitHub链接已移除")
        else:
            print("❌ 中文副标题修改失败")
            
        # 检查是否有footer_info
        if "footer_info" in LOCALES:
            print("✅ footer_info已添加")
        else:
            print("❌ footer_info未找到")
            
    except Exception as e:
        print(f"❌ locales检查失败: {e}")
    
    # 2. 检查interface文件
    try:
        from llamafactory.webui.interface import create_ui
        print("✅ interface模块加载成功")
    except Exception as e:
        print(f"❌ interface检查失败: {e}")
    
    # 3. 检查footer文件
    try:
        from llamafactory.webui.components.footer import create_footer
        print("✅ footer组件加载成功")
    except Exception as e:
        print(f"❌ footer检查失败: {e}")
    
    print("\n📋 修改总结:")
    print("1. 标题: '魔法实验室·医疗模型微调工坊'")
    print("2. 副标题: '江阴市人民医院·睡眠魔法师团队'")
    print("3. 移除了GitHub链接")
    print("4. 支持多语言切换")
    
    print("\n🚀 可以通过以下方式启动Web UI:")
    print("   python src/webui.py")
    print("   或")
    print("   llamafactory-cli webui")

if __name__ == "__main__":
    main()

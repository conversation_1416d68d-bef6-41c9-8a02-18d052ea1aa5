#!/usr/bin/env python3
"""
测试UI修改的脚本
验证标题、副标题和底部信息是否正确显示
"""

import sys
import os
sys.path.append('src')

from llamafactory.webui.locales import LOCALES

def test_title_modifications():
    """测试标题修改"""
    print("=== 测试标题修改 ===")
    
    # 检查中文标题
    zh_title = LOCALES["title"]["zh"]["value"]
    print(f"中文标题: {zh_title}")
    assert "魔法实验室·医疗模型微调工坊" in zh_title, "中文标题未正确修改"
    
    # 检查英文标题
    en_title = LOCALES["title"]["en"]["value"]
    print(f"英文标题: {en_title}")
    assert "Magic Lab · Medical Model Fine-tuning Workshop" in en_title, "英文标题未正确修改"
    
    print("✅ 标题修改测试通过")

def test_subtitle_modifications():
    """测试副标题修改（移除GitHub链接）"""
    print("\n=== 测试副标题修改 ===")
    
    # 检查中文副标题
    zh_subtitle = LOCALES["subtitle"]["zh"]["value"]
    print(f"中文副标题: {zh_subtitle}")
    assert "江阴市人民医院·睡眠魔法师团队" in zh_subtitle, "中文副标题未正确修改"
    assert "github.com" not in zh_subtitle.lower(), "GitHub链接未被移除"
    
    # 检查英文副标题
    en_subtitle = LOCALES["subtitle"]["en"]["value"]
    print(f"英文副标题: {en_subtitle}")
    assert "Jiangyin People's Hospital · Sleep Magic Team" in en_subtitle, "英文副标题未正确修改"
    assert "github.com" not in en_subtitle.lower(), "GitHub链接未被移除"
    
    print("✅ 副标题修改测试通过")

def test_footer_info():
    """测试底部信息添加"""
    print("\n=== 测试底部信息 ===")
    
    # 检查是否添加了footer_info
    assert "footer_info" in LOCALES, "footer_info未添加到LOCALES中"
    
    # 检查中文底部信息
    zh_footer = LOCALES["footer_info"]["zh"]["value"]
    print(f"中文底部信息: {zh_footer}")
    assert "江阴市人民医院·睡眠魔法师团队" in zh_footer, "中文底部信息未正确添加"
    
    # 检查英文底部信息
    en_footer = LOCALES["footer_info"]["en"]["value"]
    print(f"英文底部信息: {en_footer}")
    assert "Jiangyin People's Hospital · Sleep Magic Team" in en_footer, "英文底部信息未正确添加"
    
    print("✅ 底部信息测试通过")

def test_all_languages():
    """测试所有语言的修改"""
    print("\n=== 测试所有语言支持 ===")
    
    languages = ["en", "ru", "zh", "ko", "ja"]
    
    for lang in languages:
        print(f"\n检查 {lang} 语言:")
        
        # 检查标题
        title = LOCALES["title"][lang]["value"]
        print(f"  标题: {title}")
        
        # 检查副标题
        subtitle = LOCALES["subtitle"][lang]["value"]
        print(f"  副标题: {subtitle}")
        
        # 检查底部信息
        footer = LOCALES["footer_info"][lang]["value"]
        print(f"  底部信息: {footer}")
        
        # 确保没有GitHub链接
        assert "github.com" not in subtitle.lower(), f"{lang}语言的副标题仍包含GitHub链接"
    
    print("✅ 所有语言测试通过")

if __name__ == "__main__":
    try:
        test_title_modifications()
        test_subtitle_modifications()
        test_footer_info()
        test_all_languages()
        
        print("\n🎉 所有测试通过！UI修改成功完成。")
        print("\n修改总结:")
        print("1. ✅ 标题已修改为'魔法实验室·医疗模型微调工坊'")
        print("2. ✅ 移除了'访问 GitHub 主页'链接")
        print("3. ✅ 添加了'江阴市人民医院·睡眠魔法师团队'底部信息")
        print("4. ✅ 支持多语言切换")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
